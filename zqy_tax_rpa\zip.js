const fs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');
const { exec } = require('child_process');
// 指定要监听的文件夹路径
const folderPath = 'C:/Users/<USER>/Downloads/RPAdownload';
const pdfFolder = '';
// 创建一个文件夹监听器
const watcher = fs.watch(folderPath, async (eventType, filename) => {
    if (eventType === 'rename') { // 'rename' 事件在文件创建或删除时触发
        const filePath = path.join(folderPath, filename);
        // 检查文件是否存在（防止误报删除事件）
        if (fs.existsSync(filePath)) {
            // 检查文件是否为压缩文件（这里以 .zip 为例）
            if (path.extname(filename).toLowerCase() === '.zip') {
                console.log(`检测到新的压缩文件: ${filename}`);
                try {
                    // 创建一个新的 AdmZip 实例
                    const zip = new AdmZip(filePath);
                    // 解压文件到指定的文件夹（这里解压到与压缩文件相同的文件夹）
                    zip.extractAllTo(folderPath, true);
                    console.log(`解压完成: ${filename}`);
                    // 解压完成后删除压缩文件
                    await fs.unlinkSync(filePath);
                    console.log(`已删除压缩文件: ${filename}`);

                    // 解压完成后执行 uploadPDF.js
                    console.log('开始执行 uploadPDF.js...');
                    exec('node uploadPDF.js', (error, stdout, stderr) => {
                        if (error) {
                            console.error(`执行 uploadPDF.js 时出错: ${error}`);
                            return;
                        }
                        if (stdout) {
                            console.log(`uploadPDF.js 输出: ${stdout}`);
                        }
                        if (stderr) {
                            console.error(`uploadPDF.js 错误输出: ${stderr}`);
                        }
                        console.log(`uploadPDF.js 执行完成`);
                    });

                } catch (err) {
                    console.error(`解压文件时出错: ${err}`);
                }
            }
        }
    }
});
console.log(`正在监听文件夹: ${folderPath}`);
// 监听器错误处理
watcher.on('error', (err) => {
    console.error(`监听文件夹时出错: ${err}`);
});