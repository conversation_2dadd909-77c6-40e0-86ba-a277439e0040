const Untils = require("./untils");
const untils = new Untils();
const fs = require('fs');
const path = require('path');
// 指定要监听的文件夹路径
const folderPath = 'C:/Users/<USER>/Downloads/RPAdownload';
const ossUrl = 'https://zqcloud.shuzutech.com'
const OSS = require('ali-oss');
const client = new OSS({
    endpoint: 'oss-cn-shanghai.aliyuncs.com',
    accessKeyId: 'LTAI5tEZoVaq5zroB7urbauK',
    accessKeySecret: '******************************',
    bucket: 'zhenqi-cloud',
});
// 创建一个文件夹监听器
const watcher = fs.watch(folderPath, (eventType, filename) => {
  if (eventType === 'rename') {
    // 'add' 事件在文件创建或删除时触发
    // 'rename' 事件在文件创建或删除时触发
    const filePath = path.join(folderPath, filename);
    console.log(`检测到文件路径: ${filePath}`);
    // 使用 setTimeout 延迟一段时间检查文件存在性
    setTimeout(async () => {
      try {
        await fs.promises.access(filePath, fs.constants.F_OK);
        // 检查文件是否为PDF文件（这里以 .pdf 为例）
        if (path.extname(filename).toLowerCase() === '.pdf') {
            console.log(`检测到新的PDF文件: ${filename}`);
            const fileName = '/invoice/' + filename;
            let file =  ossUrl+fileName;
            try {
                await client.put(fileName, filePath);
                console.log('文件上传成功:', file);
                // untils.addLogForPDF('uploadPDF', '上传PDF文件成功:'+ file, 'uploadPDF')
                await fs.promises.unlink(filePath);
                console.log('文件已删除:', filePath);
            } catch (error) {
                console.error('文件上传失败:', error);
            }
        }
      } catch (err) {
        // 文件不存在或无法访问
        console.error(`文件不存在或无法访问: ${filePath}`);
      }
    }, 200); // 延迟200毫秒
  }
});
console.log(`正在监听文件夹: ${folderPath}`);
// 监听器错误处理
watcher.on('error', (err) => {
  console.error(`监听文件夹时出错: ${err}`);
});