const { getherInvoice, downloadEXCEL, downloadPDF, getPDF } = require('./Download.js')
const pbottleRPA = require("./pbottleRPA");
const { spawn } = require('child_process');

main()

async function main(){
    // await getPDF()
    // pbottleRPA.browserCMD_click('div.t-table__content > table > thead > tr > th > div > label > span')
    // await downloadEXCEL()
    // await downloadPDF()

    // 同时启动 zip.js 和 uploadPDF.js
    const processes = await startBothScripts();

    // 等待5秒
    await pbottleRPA.sleep(5000);

    // 关闭两个进程
    console.log('正在关闭所有进程...');

    // 终止 zip 进程
    if (processes.zipProcess && !processes.zipProcess.killed) {
        console.log('终止 ZIP 进程...');
        processes.zipProcess.kill('SIGTERM');

        // 如果进程在3秒内没有停止，强制杀死
        setTimeout(() => {
            if (processes.zipProcess && !processes.zipProcess.killed) {
                console.log('强制杀死 ZIP 进程');
                processes.zipProcess.kill('SIGKILL');
            }
        }, 3000);
    }

    // 终止 upload 进程
    if (processes.uploadProcess && !processes.uploadProcess.killed) {
        console.log('终止 UPLOAD 进程...');
        processes.uploadProcess.kill('SIGTERM');

        // 如果进程在3秒内没有停止，强制杀死
        setTimeout(() => {
            if (processes.uploadProcess && !processes.uploadProcess.killed) {
                console.log('强制杀死 UPLOAD 进程');
                processes.uploadProcess.kill('SIGKILL');
            }
        }, 3000);
    }

    console.log('所有进程已终止');
}

// 同时执行两个脚本的函数
async function startBothScripts() {
    console.log('正在启动 zip.js 和 uploadPDF.js...');

    // 启动 zip.js 进程
    const zipProcess = spawn('node', ['zip.js'], {
        cwd: __dirname,
        stdio: 'pipe'
    });

    // 启动 uploadPDF.js 进程
    const uploadProcess = spawn('node', ['uploadPDF.js'], {
        cwd: __dirname,
        stdio: 'pipe'
    });

    // 处理 zip.js 的输出
    zipProcess.stdout.on('data', (data) => {
        console.log(`[ZIP] ${data.toString().trim()}`);
    });

    zipProcess.stderr.on('data', (data) => {
        console.error(`[ZIP ERROR] ${data.toString().trim()}`);
    });

    zipProcess.on('close', (code) => {
        console.log(`[ZIP] 进程退出，退出码: ${code}`);
    });

    // 处理 uploadPDF.js 的输出
    uploadProcess.stdout.on('data', (data) => {
        console.log(`[UPLOAD] ${data.toString().trim()}`);
    });

    uploadProcess.stderr.on('data', (data) => {
        console.error(`[UPLOAD ERROR] ${data.toString().trim()}`);
    });

    uploadProcess.on('close', (code) => {
        console.log(`[UPLOAD] 进程退出，退出码: ${code}`);
    });

    // 处理进程错误
    zipProcess.on('error', (err) => {
        console.error(`[ZIP] 启动进程时出错: ${err}`);
    });

    uploadProcess.on('error', (err) => {
        console.error(`[UPLOAD] 启动进程时出错: ${err}`);
    });

    console.log('两个脚本已启动，正在后台运行...');
    console.log('按 Ctrl+C 退出程序');

    // 优雅退出处理
    process.on('SIGINT', () => {
        console.log('\n正在关闭所有进程...');
        zipProcess.kill('SIGTERM');
        uploadProcess.kill('SIGTERM');
        process.exit(0);
    });

    // 保持主进程运行
    return new Promise(() => {
        // 这个 Promise 不会 resolve，保持程序运行
        // 程序会一直运行直到用户按 Ctrl+C
    });
}