const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class ProcessManager {
    constructor() {
        this.processes = new Map();
        this.pidFile = path.join(__dirname, 'processes.json');
    }

    // 启动单个进程
    startProcess(name, scriptPath, args = []) {
        if (this.processes.has(name)) {
            console.log(`进程 ${name} 已经在运行中`);
            return false;
        }

        console.log(`启动进程: ${name}`);
        const process = spawn('node', [scriptPath, ...args], {
            cwd: __dirname,
            stdio: 'pipe',
            detached: false
        });

        // 保存进程信息
        this.processes.set(name, {
            process: process,
            pid: process.pid,
            scriptPath: scriptPath,
            startTime: new Date(),
            status: 'running'
        });

        // 处理输出
        process.stdout.on('data', (data) => {
            console.log(`[${name.toUpperCase()}] ${data.toString().trim()}`);
        });

        process.stderr.on('data', (data) => {
            console.error(`[${name.toUpperCase()} ERROR] ${data.toString().trim()}`);
        });

        process.on('close', (code) => {
            console.log(`[${name.toUpperCase()}] 进程退出，退出码: ${code}`);
            const processInfo = this.processes.get(name);
            if (processInfo) {
                processInfo.status = 'stopped';
                processInfo.exitCode = code;
                processInfo.endTime = new Date();
            }
        });

        process.on('error', (err) => {
            console.error(`[${name.toUpperCase()}] 启动进程时出错: ${err}`);
            this.processes.delete(name);
        });

        this.savePidFile();
        return true;
    }

    // 停止单个进程
    stopProcess(name) {
        const processInfo = this.processes.get(name);
        if (!processInfo) {
            console.log(`进程 ${name} 不存在或已停止`);
            return false;
        }

        if (processInfo.process && !processInfo.process.killed) {
            console.log(`停止进程: ${name} (PID: ${processInfo.pid})`);
            processInfo.process.kill('SIGTERM');
            processInfo.status = 'stopping';
            
            // 如果进程在5秒内没有停止，强制杀死
            setTimeout(() => {
                if (processInfo.process && !processInfo.process.killed) {
                    console.log(`强制杀死进程: ${name}`);
                    processInfo.process.kill('SIGKILL');
                }
            }, 5000);
            
            return true;
        }
        return false;
    }

    // 停止所有进程
    stopAllProcesses() {
        console.log('停止所有进程...');
        for (const [name] of this.processes) {
            this.stopProcess(name);
        }
    }

    // 重启进程
    restartProcess(name) {
        const processInfo = this.processes.get(name);
        if (!processInfo) {
            console.log(`进程 ${name} 不存在`);
            return false;
        }

        const scriptPath = processInfo.scriptPath;
        this.stopProcess(name);
        
        setTimeout(() => {
            this.startProcess(name, scriptPath);
        }, 2000);
        
        return true;
    }

    // 显示所有进程状态
    showStatus() {
        console.log('\n=== 进程状态 ===');
        if (this.processes.size === 0) {
            console.log('没有运行的进程');
            return;
        }

        for (const [name, info] of this.processes) {
            const uptime = info.status === 'running' ? 
                Math.floor((new Date() - info.startTime) / 1000) : 0;
            
            console.log(`${name}:`);
            console.log(`  状态: ${info.status}`);
            console.log(`  PID: ${info.pid}`);
            console.log(`  脚本: ${info.scriptPath}`);
            console.log(`  启动时间: ${info.startTime.toLocaleString()}`);
            if (info.status === 'running') {
                console.log(`  运行时长: ${uptime}秒`);
            }
            if (info.endTime) {
                console.log(`  结束时间: ${info.endTime.toLocaleString()}`);
                console.log(`  退出码: ${info.exitCode}`);
            }
            console.log('');
        }
    }

    // 保存进程信息到文件
    savePidFile() {
        const processData = {};
        for (const [name, info] of this.processes) {
            processData[name] = {
                pid: info.pid,
                scriptPath: info.scriptPath,
                startTime: info.startTime,
                status: info.status
            };
        }
        
        try {
            fs.writeFileSync(this.pidFile, JSON.stringify(processData, null, 2));
        } catch (err) {
            console.error('保存进程信息失败:', err);
        }
    }

    // 从文件加载进程信息
    loadPidFile() {
        try {
            if (fs.existsSync(this.pidFile)) {
                const data = fs.readFileSync(this.pidFile, 'utf8');
                const processData = JSON.parse(data);
                
                // 检查进程是否还在运行
                for (const [name, info] of Object.entries(processData)) {
                    if (this.isProcessRunning(info.pid)) {
                        console.log(`发现运行中的进程: ${name} (PID: ${info.pid})`);
                        // 这里可以选择重新连接到现有进程或杀死它们
                    }
                }
            }
        } catch (err) {
            console.error('加载进程信息失败:', err);
        }
    }

    // 检查进程是否在运行
    isProcessRunning(pid) {
        try {
            process.kill(pid, 0);
            return true;
        } catch (err) {
            return false;
        }
    }

    // 通过PID杀死进程
    killProcessByPid(pid) {
        try {
            process.kill(pid, 'SIGTERM');
            console.log(`已发送终止信号给进程 PID: ${pid}`);
            
            setTimeout(() => {
                if (this.isProcessRunning(pid)) {
                    process.kill(pid, 'SIGKILL');
                    console.log(`强制杀死进程 PID: ${pid}`);
                }
            }, 5000);
            
            return true;
        } catch (err) {
            console.error(`杀死进程失败 PID ${pid}:`, err.message);
            return false;
        }
    }

    // 清理资源
    cleanup() {
        this.stopAllProcesses();
        
        // 删除PID文件
        try {
            if (fs.existsSync(this.pidFile)) {
                fs.unlinkSync(this.pidFile);
            }
        } catch (err) {
            console.error('删除PID文件失败:', err);
        }
    }
}

module.exports = ProcessManager;

// 如果直接运行此文件，提供命令行接口
if (require.main === module) {
    const manager = new ProcessManager();
    const readline = require('readline');
    
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    console.log('进程管理器已启动');
    console.log('可用命令:');
    console.log('  start <name> <script> - 启动进程');
    console.log('  stop <name> - 停止进程');
    console.log('  restart <name> - 重启进程');
    console.log('  status - 查看所有进程状态');
    console.log('  kill <pid> - 通过PID杀死进程');
    console.log('  stopall - 停止所有进程');
    console.log('  quit - 退出管理器');

    rl.on('line', (input) => {
        const parts = input.trim().split(' ');
        const command = parts[0].toLowerCase();

        switch (command) {
            case 'start':
                if (parts.length >= 3) {
                    manager.startProcess(parts[1], parts[2]);
                } else {
                    console.log('用法: start <name> <script>');
                }
                break;

            case 'stop':
                if (parts.length >= 2) {
                    manager.stopProcess(parts[1]);
                } else {
                    console.log('用法: stop <name>');
                }
                break;

            case 'restart':
                if (parts.length >= 2) {
                    manager.restartProcess(parts[1]);
                } else {
                    console.log('用法: restart <name>');
                }
                break;

            case 'status':
                manager.showStatus();
                break;

            case 'kill':
                if (parts.length >= 2) {
                    const pid = parseInt(parts[1]);
                    if (!isNaN(pid)) {
                        manager.killProcessByPid(pid);
                    } else {
                        console.log('PID必须是数字');
                    }
                } else {
                    console.log('用法: kill <pid>');
                }
                break;

            case 'stopall':
                manager.stopAllProcesses();
                break;

            case 'quit':
            case 'exit':
                console.log('正在退出...');
                manager.cleanup();
                rl.close();
                process.exit(0);
                break;

            default:
                console.log('未知命令');
        }
    });

    process.on('SIGINT', () => {
        console.log('\n正在退出...');
        manager.cleanup();
        process.exit(0);
    });
}
